import { inject, Injectable } from '@angular/core';
import { SplunkLoggerService } from '@decision-services/angular-splunk-logger';
import { BannerMessage, MessageSeverity, MessageType } from '@rocket-logic/rl-xp-bff-models';
import { catchError, combineLatest, map, of, shareReplay, startWith } from 'rxjs';
import { environment } from '../../../environments/environment';
import { LoanStateService } from '../entity-state/loan-state/loan-state.service';
import { NotificationService } from '../notification/notification.service';

@Injectable({
  providedIn: 'root',
})
export class BannerMessageService {
  private notificationService = inject(NotificationService);
  private logger = inject(SplunkLoggerService);
  private loanStateService = inject(LoanStateService, { optional: true });

  // Create archived loan banner message
  private createArchivedLoanMessage(): BannerMessage {
    return {
      messageId: 'archived-loan',
      messageType: MessageType.BannerMessage,
      subject: 'Archived Loan',
      content: 'This loan is archived and cannot be edited. Any changes will not be saved.',
      messageSeverity: MessageSeverity.Warn,
      isActive: true,
      timestamp: new Date().toISOString(),
      source: 'rl-xp-ui',
    };
  }

  // Get server banner messages from BFF
  private getServerBannerMessages() {
    return this.notificationService
      .getConnection$(`${environment.dataProviderUrl}/messageCenter/listen/bannerMessages`)
      .pipe(
        map((message): BannerMessage[] => {
          const data = JSON.parse(message.data || '[]');
          if (!Array.isArray(data)) {
            return [];
          }
          return data.filter((message: BannerMessage) => message.isActive);
        }),
        catchError((err) => {
          this.logger.error('Error while listening for banner messages', err);
          return of([]);
        }),
        startWith([])
      );
  }

  // Combine server messages with archived loan message when applicable
  public readonly bannerMessages$ = this.loanStateService
    ? combineLatest([
        this.getServerBannerMessages(),
        this.loanStateService.isLoanArchived$
      ]).pipe(
        map(([serverMessages, isLoanArchived]) => {
          const messages = [...serverMessages];

          // Add archived loan message if loan is archived (at the beginning for priority)
          if (isLoanArchived) {
            messages.unshift(this.createArchivedLoanMessage());
          }

          return messages;
        }),
        shareReplay(1)
      )
    : this.getServerBannerMessages().pipe(shareReplay(1));
}
