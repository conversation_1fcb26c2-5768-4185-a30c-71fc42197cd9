import { ComponentFixture, TestBed } from '@angular/core/testing';

import { signal } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { MockBuilder, MockInstance } from 'ng-mocks';
import { NEVER } from 'rxjs';
import { FormattedNumberInputComponent } from '../../question-input/formatted-number-input/formatted-number-input.component';
import { AssetFormService } from '../../services/entity-state/asset-state/asset-form.service';
import { AssetStateService } from '../../services/entity-state/asset-state/asset-state.service';
import { ClientStateService } from '../../services/entity-state/client-state/client-state.service';
import { LoanStateService } from '../../services/entity-state/loan-state/loan-state.service';
import { UserAuthorizationService } from '../../services/user-authorization/user-authorization.service';
import { BaseAssetComponent } from './base-asset.component';

describe('BaseAssetComponent', () => {
  let component: BaseAssetComponent;
  let fixture: ComponentFixture<BaseAssetComponent>;

  beforeEach(() =>
    MockBuilder(BaseAssetComponent)
      .mock(ClientStateService, {
        state: signal([]),
      })
      .mock(LoanStateService, {
        isLoanEditingDisabled$: NEVER,
      })
      .mock(AssetStateService, { deletionState: signal({}) })
      .mock(AssetFormService)
      .mock(UserAuthorizationService),
  );

  beforeEach(() => {
    MockInstance(FormattedNumberInputComponent, 'matFormField', signal(undefined));
    fixture = TestBed.createComponent(BaseAssetComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('formGroup', new FormGroup({}));
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
